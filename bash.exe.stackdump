Stack trace:
Frame         Function      Args
0007FFFF8B80  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF8B80, 0007FFFF7A80) msys-2.0.dll+0x2118E
0007FFFF8B80  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF8B80  0002100469F2 (00021028DF99, 0007FFFF8A38, 0007FFFF8B80, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8B80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8B80  00021006A545 (0007FFFF8B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF8B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF963C60000 ntdll.dll
7FF962790000 KERNEL32.DLL
7FF961410000 KERNELBASE.dll
7FF961AC0000 USER32.dll
7FF9613E0000 win32u.dll
7FF962060000 GDI32.dll
7FF961200000 gdi32full.dll
7FF961800000 msvcp_win.dll
7FF9618B0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF961A00000 advapi32.dll
7FF963000000 msvcrt.dll
7FF962240000 sechost.dll
7FF962EE0000 RPCRT4.dll
7FF9603B0000 CRYPTBASE.DLL
7FF961340000 bcryptPrimitives.dll
7FF962200000 IMM32.DLL
